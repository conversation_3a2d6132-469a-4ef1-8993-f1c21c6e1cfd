import { createClient } from "@supabase/supabase-js"

// This function creates a Supabase client for server-side operations.
// It uses the service role key for elevated privileges, which is safe on the server.
export function createSupabaseServerClient() {
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
    throw new Error("Supabase environment variables are not set. Please add them to your .env file.")
  }

  return createClient(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY)
}
