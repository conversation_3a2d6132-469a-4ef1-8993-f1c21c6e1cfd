import { ListChecks, Activity, Shield, Clock } from "lucide-react"

const features = [
  {
    name: "Manage Multiple Queues",
    description:
      "Don't limit your chances. Run multiple queues at the same time, significantly increasing your odds of getting in faster.",
    icon: ListChecks,
  },
  {
    name: "Automatic Position Monitoring",
    description: "Never lose your spot. We watch your place in line 24/7 and alert you the second it's your turn.",
    icon: Activity,
  },
  {
    name: "Stay Ready for Access",
    description:
      "Our system keeps your session active so you never time out or get disconnected. You'll be ready the moment the doors open.",
    icon: Shield,
  },
  {
    name: "Set It and Forget It",
    description:
      "Focus on what matters to you. <PERSON><PERSON><PERSON><PERSON> works invisibly in the background, handling the tedious waiting so you don't have to.",
    icon: Clock,
  },
]

export default function Features() {
  return (
    <section className="container space-y-16 py-24 md:py-32">
      <div className="mx-auto max-w-[58rem] text-center">
        <h2 className="font-bold text-3xl leading-[1.1] sm:text-3xl md:text-5xl">Your Automated Queue Assistant</h2>
        <p className="mt-4 text-muted-foreground sm:text-lg">
          Ka<PERSON>-Q is packed with powerful features designed to make your life easier.
        </p>
      </div>
      <div className="mx-auto grid max-w-5xl grid-cols-1 gap-8 md:grid-cols-2">
        {features.map((feature) => (
          <div key={feature.name} className="relative overflow-hidden rounded-lg border bg-background p-6 md:p-8">
            <div className="flex items-center gap-4">
              <feature.icon className="h-8 w-8 text-primary" />
              <h3 className="font-bold">{feature.name}</h3>
            </div>
            <p className="mt-2 text-muted-foreground">{feature.description}</p>
          </div>
        ))}
      </div>
    </section>
  )
}
