"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Github, Mail, Menu, X, FileText } from "lucide-react"

export default function Navbar() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen)
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 max-w-screen-2xl items-center justify-between">
        <Link href="/" className="flex items-center space-x-2">
          <Image
            src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/KASPER-Q-CpXYzhhLcVBWYHskShYUvJfxpGfYon.png"
            alt="KASPER-Q Logo"
            width={180}
            height={60}
            className="h-8 w-auto"
          />
        </Link>

        {/* Mobile menu button */}
        <Button
          variant="ghost"
          size="icon"
          className="md:hidden"
          onClick={toggleMobileMenu}
          aria-label={mobileMenuOpen ? "Close menu" : "Open menu"}
        >
          {mobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
        </Button>

        {/* Desktop navigation */}
        <div className="hidden md:flex items-center space-x-4">
          <Link href="https://help.kasperq.com" target="_blank" rel="noopener noreferrer">
            <Button variant="ghost" size="sm" className="flex items-center gap-1">
              <FileText className="h-4 w-4" />
              Docs
            </Button>
          </Link>
          <Link href="https://github.com/kasper-q" target="_blank" rel="noreferrer">
            <Button variant="ghost" size="sm" className="flex items-center gap-1">
              <Github className="h-4 w-4" />
              GitHub
            </Button>
          </Link>
          <a href="mailto:<EMAIL>">
            <Button variant="ghost" size="sm" className="flex items-center gap-1">
              <Mail className="h-4 w-4" />
              Contact
            </Button>
          </a>
          <Link href="https://whop.com/kasper-q" target="_blank" rel="noopener noreferrer">
            <Button size="sm">Dashboard</Button>
          </Link>
        </div>
      </div>

      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className="md:hidden border-t border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="container py-3 flex flex-col space-y-3">
            <Link
              href="https://help.kasperq.com"
              target="_blank"
              rel="noopener noreferrer"
              onClick={() => setMobileMenuOpen(false)}
            >
              <Button variant="ghost" size="sm" className="w-full justify-start">
                <FileText className="h-4 w-4 mr-2" />
                Docs
              </Button>
            </Link>
            <Link
              href="https://github.com/kasper-q"
              target="_blank"
              rel="noreferrer"
              onClick={() => setMobileMenuOpen(false)}
            >
              <Button variant="ghost" size="sm" className="w-full justify-start">
                <Github className="h-4 w-4 mr-2" />
                GitHub
              </Button>
            </Link>
            <a href="mailto:<EMAIL>" onClick={() => setMobileMenuOpen(false)}>
              <Button variant="ghost" size="sm" className="w-full justify-start">
                <Mail className="h-4 w-4 mr-2" />
                Contact
              </Button>
            </a>
            <Link
              href="https://whop.com/kasper-q"
              target="_blank"
              rel="noopener noreferrer"
              onClick={() => setMobileMenuOpen(false)}
            >
              <Button size="sm" className="w-full justify-start">
                Dashboard
              </Button>
            </Link>
          </div>
        </div>
      )}
    </header>
  )
}
