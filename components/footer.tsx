import Link from "next/link"
import Image from "next/image"
import { Github } from "lucide-react"

export default function Footer() {
  return (
    <footer className="border-t">
      <div className="container py-4 md:py-6">
        <div className="flex flex-col md:flex-row md:justify-between md:items-end">
          <div className="flex flex-col items-start text-left mb-4 md:mb-0">
            <Link href="/" className="inline-block mb-3">
              <Image
                src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/KASPER-Q-CpXYzhhLcVBWYHskShYUvJfxpGfYon.png"
                alt="KASPER-Q Logo"
                width={180}
                height={60}
                className="h-8 w-auto"
              />
            </Link>
            <p className="text-sm text-muted-foreground max-w-md">
              Pioneering queue management solutions for high-demand online experiences.
            </p>
          </div>

          <div className="flex flex-col items-start md:items-end">
            <h4 className="font-semibold text-sm mb-2">Connect With Us</h4>
            <div className="flex space-x-4">
              <Link
                href="https://github.com/kasper-q"
                className="text-muted-foreground transition-colors hover:text-primary"
              >
                <Github className="h-5 w-5" />
                <span className="sr-only">GitHub</span>
              </Link>
              <Link
                href="https://x.com/queueithandler"
                className="text-muted-foreground transition-colors hover:text-primary"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="h-5 w-5 fill-current">
                  <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                </svg>
                <span className="sr-only">X (formerly Twitter)</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
      <div className="container border-t py-3">
        <p className="text-left text-sm text-muted-foreground">
          © {new Date().getFullYear()} KASPER-Q, Inc. All rights reserved.
        </p>
      </div>
    </footer>
  )
}
