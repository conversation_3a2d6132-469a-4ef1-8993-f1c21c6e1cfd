import { Music, ShoppingBag, Zap, Code, Trophy } from "lucide-react"

const userCategories = [
  {
    name: "Concert and Festival Enthusiasts",
    description: "Secure tickets to sold-out events",
    icon: Music,
  },
  {
    name: "Sports Events Fans",
    description: "Never miss getting tickets to big games",
    icon: Trophy,
  },
  {
    name: "Limited Edition Collectors",
    description: "Be first in line for product drops",
    icon: ShoppingBag,
  },
  {
    name: "Online Shopping Experts",
    description: "Access flash sales and limited-time offers",
    icon: Zap,
  },
  {
    name: "Developers",
    description: "Integrate our API into your custom solutions",
    icon: Code,
  },
]

export default function PerfectFor() {
  return (
    <section className="container space-y-16 py-24 md:py-32 border-t">
      <div className="mx-auto max-w-[58rem] text-center">
        <h2 className="font-bold text-3xl leading-[1.1] sm:text-3xl md:text-5xl">Perfect For</h2>
        <p className="mt-4 text-muted-foreground sm:text-lg">
          Ka<PERSON>-<PERSON> serves a wide range of users who need priority access to high-demand online experiences.
        </p>
      </div>
      <div className="mx-auto grid max-w-6xl grid-cols-1 gap-8 md:grid-cols-3 lg:grid-cols-5">
        {userCategories.map((category) => (
          <div key={category.name} className="relative overflow-hidden rounded-lg border bg-background p-6 text-center">
            <category.icon className="h-12 w-12 mx-auto mb-4" />
            <h3 className="font-bold mb-2">{category.name}</h3>
            <p className="text-muted-foreground">{category.description}</p>
          </div>
        ))}
      </div>
    </section>
  )
}
