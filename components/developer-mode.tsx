"use client"

import { useState, useActionState, useEffect, useRef } from "react"
import { Code, Terminal, GitBranch, Clock, Mail, CheckCircle, AlertCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { addToWaitlist } from "@/app/waitlist/action"

const developerFeatures = [
  {
    icon: <Code className="h-5 w-5 text-primary flex-shrink-0" />,
    text: "Build custom modules for specific websites",
  },
  {
    icon: <Terminal className="h-5 w-5 text-primary flex-shrink-0" />,
    text: "Provide clients with seamless queue management",
  },
  {
    icon: <GitBranch className="h-5 w-5 text-primary flex-shrink-0" />,
    text: "Implement sophisticated queue handling without understanding complex mechanics",
  },
  {
    icon: <Clock className="h-5 w-5 text-primary flex-shrink-0" />,
    text: "Save development time with our pre-built infrastructure",
  },
]

const codeExamples = {
  javascript: `// Initialize Kasper-Q client
import { KasperQ } from '@kasper-q/api';

// Create a new queue monitor
const queueMonitor = new KasperQ.QueueMonitor({
  apiKey: 'YOUR_API_KEY',
  targetUrl: 'https://event-tickets.example.com',
  options: {
    maxRetries: 3,
    notifyOnPosition: 10,
    autoCheckout: true
  }
});

// Listen for position updates
queueMonitor.on('positionUpdate', (position) => {
  console.log(\`Current position: \${position}\`);
});

// Handle when queue is ready
queueMonitor.on('ready', async () => {
  console.log('Queue position reached!');
  // Proceed with checkout logic
});`,

  python: `# Initialize Kasper-Q client
from kasper_q import KasperQ

# Create a new queue monitor
queue_monitor = KasperQ.QueueMonitor(
    api_key='YOUR_API_KEY',
    target_url='https://event-tickets.example.com',
    options={
        'max_retries': 3,
        'notify_on_position': 10,
        'auto_checkout': True
    }
)

# Listen for position updates
@queue_monitor.on('position_update')
def handle_position_update(position):
    print(f"Current position: {position}")

# Handle when queue is ready
@queue_monitor.on('ready')
async def handle_ready():
    print("Queue position reached!")
    # Proceed with checkout logic`,
}

function WaitlistForm() {
  const [state, formAction, isPending] = useActionState(addToWaitlist, { success: false, message: "" })
  const formRef = useRef<HTMLFormElement>(null)

  useEffect(() => {
    if (state.success) {
      formRef.current?.reset()
    }
  }, [state.success])

  return (
    <div className="mt-8">
      <p className="font-semibold mb-3">Apply for Developer Access</p>
      <form ref={formRef} action={formAction} className="flex flex-col sm:flex-row gap-3">
        <div className="relative flex-grow">
          <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
          <Input type="email" name="email" placeholder="<EMAIL>" required className="pl-10 h-11" />
        </div>
        <Button type="submit" size="lg" disabled={isPending} className="h-11">
          {isPending ? "Applying..." : "Apply"}
        </Button>
      </form>
      {state.message && (
        <div className={`mt-3 flex items-center text-sm ${state.success ? "text-green-500" : "text-red-500"}`}>
          {state.success ? <CheckCircle className="h-4 w-4 mr-2" /> : <AlertCircle className="h-4 w-4 mr-2" />}
          {state.message}
        </div>
      )}
    </div>
  )
}

export default function DeveloperMode() {
  const [activeTab, setActiveTab] = useState("javascript")

  return (
    <section className="container py-24 md:py-32 border-t">
      <div className="mx-auto max-w-6xl">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="font-bold text-3xl leading-[1.1] sm:text-3xl md:text-5xl mb-6">Developer Mode</h2>
            <p className="text-muted-foreground sm:text-lg mb-8">
              Developers, we've got you covered. Kasper-Q offers powerful APIs to integrate with various websites,
              eliminating online queue challenges from your development process.
            </p>
            <ul className="space-y-4 mb-8">
              {developerFeatures.map((feature, index) => (
                <li key={index} className="flex items-center">
                  <div className="mr-3">{feature.icon}</div>
                  <span className="text-muted-foreground">{feature.text}</span>
                </li>
              ))}
            </ul>
            <WaitlistForm />
          </div>
          <div className="bg-background/30 backdrop-blur-sm rounded-lg border border-white/10 shadow-[0_0_15px_rgba(0,0,0,0.1)] overflow-hidden">
            <div className="flex items-center justify-between px-4 py-2 bg-background/80 border-b border-white/10">
              <div className="min-w-0 flex-1 overflow-x-auto">
                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => setActiveTab("javascript")}
                    className={`flex items-center px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                      activeTab === "javascript" ? "bg-primary text-primary-foreground" : "hover:bg-background/50"
                    }`}
                  >
                    <Code className="h-3 w-3 mr-1" />
                    JavaScript
                  </button>
                  <button
                    onClick={() => setActiveTab("python")}
                    className={`flex items-center px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                      activeTab === "python" ? "bg-primary text-primary-foreground" : "hover:bg-background/50"
                    }`}
                  >
                    <Code className="h-3 w-3 mr-1" />
                    Python
                  </button>
                </div>
              </div>
              <div className="flex space-x-1">
                <div className="w-3 h-3 rounded-full bg-red-500/70"></div>
                <div className="w-3 h-3 rounded-full bg-yellow-500/70"></div>
                <div className="w-3 h-3 rounded-full bg-green-500/70"></div>
              </div>
            </div>

            <pre className="text-xs sm:text-sm md:text-base p-6 overflow-x-auto bg-gradient-to-b from-background/50 to-background/30">
              <code className={`language-${activeTab} text-foreground/90`}>{codeExamples[activeTab]}</code>
            </pre>
          </div>
        </div>
      </div>
    </section>
  )
}
