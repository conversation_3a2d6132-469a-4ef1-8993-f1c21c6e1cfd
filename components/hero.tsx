import { Button } from "@/components/ui/button"
import Image from "next/image"
import Link from "next/link"

export default function Hero() {
  return (
    <section className="container py-24 text-center md:py-32">
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-center mb-4">
          <div className="inline-block rounded-full border border-white/10 bg-background/60 px-4 py-2 text-sm font-semibold text-foreground backdrop-blur-sm">
            The Easiest Bot to Use
          </div>
        </div>
        <h1 className="bg-gradient-to-br from-foreground from-30% via-foreground/90 to-foreground/70 bg-clip-text text-4xl font-bold tracking-tight text-transparent sm:text-5xl md:text-6xl lg:text-7xl">
          Provide the Link,
          <br />
          We&apos;ll Handle the Line.
        </h1>
        <p className="mx-auto mt-6 max-w-[42rem] leading-normal text-muted-foreground sm:text-xl sm:leading-8">
          Ka<PERSON>-<PERSON> is the ultimate queue management bot. It handles multiple Queue-it systems at the same time, giving
          you faster access to high-demand events, product launches, and ticket sales.
        </p>
        <div className="mt-8 flex flex-col sm:flex-row items-center justify-center gap-4">
          <Link href="https://whop.com/checkout/plan_XMNpjfwYszSNE?d2c=true" target="_blank" rel="noopener noreferrer">
            <Button size="lg" className="flex items-center gap-2">
              <Image
                src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/whop_logo_brandmark_white-mkaxRVt50fm8a3NQgfbRZAtrNvMO03.svg"
                alt="Whop"
                width={24}
                height={24}
                className="h-5 w-5 [filter:invert(1)]"
              />
              Start your 7-day free trial now
            </Button>
          </Link>
          <Link href="https://help.kasperq.com" target="_blank" rel="noopener noreferrer">
            <Button variant="outline" size="lg">
              Docs
            </Button>
          </Link>
        </div>
      </div>

      {/* Screenshot Section */}
      <div className="relative mx-auto mt-16 md:mt-20 max-w-6xl">
        <div className="relative rounded-xl bg-background/50 p-2 sm:p-4 shadow-2xl ring-1 ring-white/10">
          <Image
            src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Screenshot_2025-07-01_at_20.17.30-m2LMUaMJowk54VRo5NoVrWrgeinHnl.png"
            alt="Kasper-Q Dashboard"
            width={1200}
            height={750}
            className="rounded-lg"
          />
        </div>
      </div>
    </section>
  )
}
