import { Button } from "@/components/ui/button"
import Image from "next/image"
import Link from "next/link"

export default function CTA() {
  return (
    <section className="border-t">
      <div className="container flex flex-col items-center gap-4 py-24 text-center md:py-32">
        <h2 className="font-bold text-3xl leading-[1.1] sm:text-3xl md:text-5xl">Ready to Skip the Line?</h2>
        <p className="max-w-[42rem] leading-normal text-muted-foreground sm:text-xl sm:leading-8">
          Join <PERSON><PERSON>-<PERSON> today and experience the fastest way to bypass virtual waiting rooms. Our friendly ghost is
          waiting to handle your queues!
        </p>
        <div className="flex flex-col sm:flex-row gap-4 mt-4">
          <Link href="https://whop.com/checkout/plan_XMNpjfwYszSNE?d2c=true" target="_blank" rel="noopener noreferrer">
            <Button size="lg" className="flex items-center gap-2">
              <Image
                src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/whop_logo_brandmark_white-mkaxRVt50fm8a3NQgfbRZAtrNvMO03.svg"
                alt="Whop"
                width={24}
                height={24}
                className="h-5 w-5 [filter:invert(1)]"
              />
              Start your 7-day free trial now
            </Button>
          </Link>
          <Link href="https://help.kasperq.com" target="_blank" rel="noopener noreferrer">
            <Button size="lg" variant="outline">
              Docs
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
}
