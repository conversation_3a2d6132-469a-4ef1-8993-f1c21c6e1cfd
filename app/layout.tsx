import "./globals.css"
import { Inter } from "next/font/google"
import type React from "react"
import type { Metadata } from "next"
import MouseMoveEffect from "@/components/mouse-move-effect"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "KASPER-Q | Your Automated Queue Assistant",
  description:
    "Never wait in an online queue again. KASPER-Q is an automated queue bot that handles multiple Queue-it systems for ticket sales, product drops, and high-demand events. Provide the link, and we'll handle the line.",
  icons: {
    icon: [
      {
        url: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Artboard%201-gxENdH1TTC5nmdu4zb56B2CCL1xyPe.png",
        type: "image/png",
      },
    ],
    shortcut: [
      {
        url: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Artboard%201-gxENdH1TTC5nmdu4zb56B2CCL1xyPe.png",
        type: "image/png",
      },
    ],
    apple: [
      {
        url: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Artboard%201-gxENdH1TTC5nmdu4zb56B2CCL1xyPe.png",
        type: "image/png",
      },
    ],
  },
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <body className={`${inter.className} bg-background text-foreground antialiased`}>
        <MouseMoveEffect />
        {children}
      </body>
    </html>
  )
}
