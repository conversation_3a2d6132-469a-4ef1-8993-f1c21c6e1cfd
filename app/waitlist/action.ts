"use server"

import { createSupabaseServerClient } from "@/lib/supabase/server"
import { z } from "zod"

// Define the schema for email validation using Zod
const emailSchema = z.string().email({ message: "Please enter a valid email address." })

// Define the shape of the state our action will return
interface ActionState {
  success: boolean
  message: string
}

export async function addToWaitlist(prevState: ActionState, formData: FormData): Promise<ActionState> {
  const email = formData.get("email") as string

  // 1. Validate the email
  const validation = emailSchema.safeParse(email)
  if (!validation.success) {
    return {
      success: false,
      message: validation.error.errors[0].message,
    }
  }

  // 2. Insert the email into the database
  const supabase = createSupabaseServerClient()
  const { error } = await supabase.from("waitlist_emails").insert({ email: validation.data })

  // 3. Handle potential errors
  if (error) {
    // Handle unique constraint violation (email already exists)
    if (error.code === "23505") {
      return {
        success: true, // Return success to avoid showing an error for an existing subscription
        message: "You're already on the list!",
      }
    }

    // Log other errors for debugging
    console.error("Supabase error:", error)
    return {
      success: false,
      message: "An unexpected error occurred. Please try again later.",
    }
  }

  // 4. Return a success message
  return {
    success: true,
    message: "You're on the list! We'll be in touch soon.",
  }
}
