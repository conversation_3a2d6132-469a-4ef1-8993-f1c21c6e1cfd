import { Button } from "@/components/ui/button"
import Image from "next/image"
import Link from "next/link"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import { Users, DollarSign, TrendingUp, Target, CheckCircle, Zap } from "lucide-react"
import type { Metadata } from "next"

export const metadata: Metadata = {
  title: "Affiliate Program | KASPER-Q - Earn with Queue Management",
  description: "Join KASPER-Q's affiliate program powered by <PERSON><PERSON> and earn recurring commissions promoting the leading automated queue management bot. Become an affiliate in just 3 clicks.",
  keywords: "affiliate program, queue management, commission, earn money, referral program, KASPER-Q, Whop affiliate",
  openGraph: {
    title: "Affiliate Program | KASPER-Q - Earn with Queue Management",
    description: "Join KASPER-Q's affiliate program powered by Whop and earn recurring commissions promoting the leading automated queue management bot.",
    type: "website",
    url: "https://kasperq.com/affiliate",
  },
  twitter: {
    card: "summary_large_image",
    title: "Affiliate Program | KASPER-Q - Earn with Queue Management",
    description: "Join <PERSON>Q's affiliate program powered by Whop and earn recurring commissions promoting the leading automated queue management bot.",
  },
}

const benefits = [
  {
    name: "30% Commission",
    description: "Earn 30% commission on every sale you refer. One of the highest commission rates in the queue management space.",
    icon: DollarSign,
  },
  {
    name: "Instant Setup",
    description: "Become an affiliate in just 3 clicks through Whop's platform. No lengthy approval process or paperwork.",
    icon: Zap,
  },
  {
    name: "Proven Product",
    description: "KASPER-Q has a track record of satisfied customers and positive reviews, making it easier to sell.",
    icon: Target,
  },
  {
    name: "Whop Dashboard",
    description: "Track your performance, commissions, and payouts through Whop's comprehensive affiliate dashboard.",
    icon: Users,
  },
]

const howItWorks = [
  {
    step: "1",
    title: "Click Add Affiliate",
    description: "Visit the Whop Affiliates page and click the Add Affiliate button to find KASPER-Q.",
  },
  {
    step: "2",
    title: "Get Your Link",
    description: "Copy your unique affiliate link from your Whop dashboard - it's generated instantly.",
  },
  {
    step: "3",
    title: "Start Earning",
    description: "Share your link and earn recurring commissions for every successful referral.",
  },
]



export default function AffiliatePage() {
  return (
    <div className="relative min-h-screen">
      {/* Background gradients */}
      <div className="pointer-events-none fixed inset-0">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-background/90 to-background" />
        <div className="absolute right-0 top-0 h-[500px] w-[500px] bg-blue-500/10 blur-[100px]" />
        <div className="absolute bottom-0 left-0 h-[500px] w-[500px] bg-purple-500/10 blur-[100px]" />
      </div>

      <div className="relative z-10">
        <Navbar />
        
        {/* Hero Section */}
        <section className="container py-24 text-center md:py-32">
          <div className="max-w-4xl mx-auto">
            <div className="flex justify-center mb-4">
              <div className="inline-block rounded-full border border-white/10 bg-background/60 px-4 py-2 text-sm font-semibold text-foreground backdrop-blur-sm">
                Start Earning Today
              </div>
            </div>
            <h1 className="bg-gradient-to-br from-foreground from-30% via-foreground/90 to-foreground/70 bg-clip-text text-4xl font-bold tracking-tight text-transparent sm:text-5xl md:text-6xl lg:text-7xl">
              Join the KASPER-Q
              <br />
              Affiliate Program
            </h1>
            <p className="mx-auto mt-6 max-w-[42rem] leading-normal text-muted-foreground sm:text-xl sm:leading-8">
              Earn competitive commissions by promoting the leading automated queue management solution. 
              Help others skip the wait while building your income.
            </p>
            <div className="mt-8 flex flex-col sm:flex-row items-center justify-center gap-4">
              <Link href="https://whop.com/kasper-q/" target="_blank" rel="noopener noreferrer">
                <Button size="lg" className="flex items-center gap-2">
                  <Image
                    src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/whop_logo_brandmark_white-mkaxRVt50fm8a3NQgfbRZAtrNvMO03.svg"
                    alt="Whop"
                    width={24}
                    height={24}
                    className="h-5 w-5 [filter:invert(1)]"
                  />
                  Become an Affiliate
                </Button>
              </Link>
              <Link href="#benefits">
                <Button variant="outline" size="lg">
                  Learn More
                </Button>
              </Link>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section id="benefits" className="container space-y-16 py-24 md:py-32">
          <div className="mx-auto max-w-[58rem] text-center">
            <h2 className="font-bold text-3xl leading-[1.1] sm:text-3xl md:text-5xl">Why Partner with KASPER-Q?</h2>
            <p className="mt-4 text-muted-foreground sm:text-lg">
              Join a program that rewards you for promoting a solution people actually need.
            </p>
          </div>
          <div className="mx-auto grid max-w-5xl grid-cols-1 gap-8 md:grid-cols-2">
            {benefits.map((benefit) => (
              <div key={benefit.name} className="relative overflow-hidden rounded-lg border bg-background p-6 md:p-8">
                <div className="flex items-center gap-4">
                  <benefit.icon className="h-8 w-8 text-primary" />
                  <h3 className="font-bold">{benefit.name}</h3>
                </div>
                <p className="mt-2 text-muted-foreground">{benefit.description}</p>
              </div>
            ))}
          </div>
        </section>

        {/* How It Works Section */}
        <section className="container py-24 md:py-32">
          <div className="mx-auto max-w-[58rem] text-center mb-16">
            <h2 className="font-bold text-3xl leading-[1.1] sm:text-3xl md:text-5xl">How It Works</h2>
            <p className="mt-4 text-muted-foreground sm:text-lg">
              Start earning in just three simple steps.
            </p>
          </div>
          <div className="mx-auto grid max-w-5xl grid-cols-1 gap-8 md:grid-cols-3">
            {howItWorks.map((step) => (
              <div key={step.step} className="text-center">
                <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-primary text-primary-foreground text-xl font-bold">
                  {step.step}
                </div>
                <h3 className="mb-2 text-xl font-bold">{step.title}</h3>
                <p className="text-muted-foreground">{step.description}</p>
              </div>
            ))}
          </div>
        </section>

        {/* Commission Structure Section */}
        <section className="container py-24 md:py-32">
          <div className="mx-auto max-w-[58rem] text-center mb-16">
            <h2 className="font-bold text-3xl leading-[1.1] sm:text-3xl md:text-5xl">Why Choose Whop Affiliates?</h2>
            <p className="mt-4 text-muted-foreground sm:text-lg">
              Powered by Whop&apos;s proven affiliate platform with transparent tracking and reliable payouts.
            </p>
          </div>
          <div className="mx-auto grid max-w-5xl grid-cols-1 gap-8 md:grid-cols-3">
            <div className="relative overflow-hidden rounded-lg border bg-background p-6 md:p-8 text-center">
              <DollarSign className="h-12 w-12 text-primary mx-auto mb-4" />
              <h3 className="mb-2 text-xl font-bold">30% Commission Rate</h3>
              <p className="text-sm text-muted-foreground">Earn 30% commission on every KASPER-Q sale you refer through Whop&apos;s platform</p>
            </div>
            <div className="relative overflow-hidden rounded-lg border bg-background p-6 md:p-8 text-center">
              <TrendingUp className="h-12 w-12 text-primary mx-auto mb-4" />
              <h3 className="mb-2 text-xl font-bold">Real-Time Tracking</h3>
              <p className="text-sm text-muted-foreground">Monitor your referrals, conversions, and earnings in real-time through Whop&apos;s dashboard</p>
            </div>
            <div className="relative overflow-hidden rounded-lg border bg-background p-6 md:p-8 text-center">
              <CheckCircle className="h-12 w-12 text-primary mx-auto mb-4" />
              <h3 className="mb-2 text-xl font-bold">Reliable Payouts</h3>
              <p className="text-sm text-muted-foreground">Whop handles all payments and ensures you get paid on time, every time</p>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="container py-24 md:py-32">
          <div className="mx-auto max-w-[58rem] text-center mb-16">
            <h2 className="font-bold text-3xl leading-[1.1] sm:text-3xl md:text-5xl">What You&apos;re Promoting</h2>
            <p className="mt-4 text-muted-foreground sm:text-lg">
              KASPER-Q is the leading queue management solution with proven results.
            </p>
          </div>
          <div className="mx-auto grid max-w-5xl grid-cols-1 gap-8 md:grid-cols-2">
            <div className="relative overflow-hidden rounded-lg border bg-background p-6 md:p-8">
              <div className="flex items-center gap-4">
                <CheckCircle className="h-8 w-8 text-primary" />
                <h3 className="font-bold">Multiple Queue Management</h3>
              </div>
              <p className="mt-2 text-muted-foreground">Handle multiple queues simultaneously for better odds</p>
            </div>
            <div className="relative overflow-hidden rounded-lg border bg-background p-6 md:p-8">
              <div className="flex items-center gap-4">
                <CheckCircle className="h-8 w-8 text-primary" />
                <h3 className="font-bold">24/7 Monitoring</h3>
              </div>
              <p className="mt-2 text-muted-foreground">Never miss your turn with automatic position tracking</p>
            </div>
            <div className="relative overflow-hidden rounded-lg border bg-background p-6 md:p-8">
              <div className="flex items-center gap-4">
                <CheckCircle className="h-8 w-8 text-primary" />
                <h3 className="font-bold">Session Management</h3>
              </div>
              <p className="mt-2 text-muted-foreground">Stay active and ready without timeouts</p>
            </div>
            <div className="relative overflow-hidden rounded-lg border bg-background p-6 md:p-8">
              <div className="flex items-center gap-4">
                <CheckCircle className="h-8 w-8 text-primary" />
                <h3 className="font-bold">Easy Setup</h3>
              </div>
              <p className="mt-2 text-muted-foreground">Just provide the link and let KASPER-Q handle the rest</p>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section id="apply" className="container py-24 md:py-32">
          <div className="mx-auto max-w-[58rem] text-center">
            <h2 className="font-bold text-3xl leading-[1.1] sm:text-3xl md:text-5xl mb-6">Ready to Start Earning?</h2>
            <p className="mb-8 text-muted-foreground sm:text-lg">
              Join the KASPER-Q affiliate program powered by Whop. Get your unique referral link in just 3 clicks.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Link href="https://whop.com/kasper-q/" target="_blank" rel="noopener noreferrer">
                <Button size="lg" className="flex items-center gap-2">
                  <Image
                    src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/whop_logo_brandmark_white-mkaxRVt50fm8a3NQgfbRZAtrNvMO03.svg"
                    alt="Whop"
                    width={24}
                    height={24}
                    className="h-5 w-5 [filter:invert(1)]"
                  />
                  Become an Affiliate
                </Button>
              </Link>
              <Link href="https://help.kasperq.com" target="_blank" rel="noopener noreferrer">
                <Button variant="outline" size="lg">
                  Documentation
                </Button>
              </Link>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </div>
  )
} 